# Example-UrlUsage.ps1
# Examples demonstrating how to use JIRA URLs with Assign-JiraTickets.ps1

# Replace with your actual Personal Access Token
$token = "your-personal-access-token-here"

Write-Host "JIRA URL Usage Examples for Assign-JiraTickets.ps1" -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Example 1: Assign tickets using JIRA URL" -ForegroundColor Yellow
Write-Host "Copy a search URL from JIRA Issue Navigator and use it directly:" -ForegroundColor White
Write-Host ""
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"$token`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?jql=assignee%20is%20EMPTY%20AND%20project%20%3D%20PROJ`" \`" -ForegroundColor Green
Write-Host "    -AssigneeUsername `"developer.name`"" -ForegroundColor Green
Write-Host ""

Write-Host "Example 2: Link tickets using both URL and JQL" -ForegroundColor Yellow
Write-Host "Use URL for main search, JQL for linking target:" -ForegroundColor White
Write-Host ""
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"$token`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?jql=project%20%3D%20PROJ%20AND%20type%20%3D%20Story`" \`" -ForegroundColor Green
Write-Host "    -LinkToJql `"key = 'EPIC-123'`" \`" -ForegroundColor Green
Write-Host "    -LinkType `"Relates`"" -ForegroundColor Green
Write-Host ""

Write-Host "Example 3: Link tickets using URLs for both parameters" -ForegroundColor Yellow
Write-Host "Use URLs for both main search and link targets:" -ForegroundColor White
Write-Host ""
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"$token`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?jql=project%20%3D%20PROJ%20AND%20type%20%3D%20Story`" \`" -ForegroundColor Green
Write-Host "    -LinkToJql `"https://jira.softlayer.local/issues/?jql=key%20%3D%20%27EPIC-123%27`" \`" -ForegroundColor Green
Write-Host "    -LinkType `"Relates`"" -ForegroundColor Green
Write-Host ""

Write-Host "Example 4: Add labels using saved filter URL" -ForegroundColor Yellow
Write-Host "Use a saved filter URL to find tickets and add labels:" -ForegroundColor White
Write-Host ""
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"$token`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?jql=status%20%3D%20%27In%20Review%27%20AND%20assignee%20%3D%20currentUser%28%29`" \`" -ForegroundColor Green
Write-Host "    -Label `"reviewed`"" -ForegroundColor Green
Write-Host ""

Write-Host "How to get JIRA URLs:" -ForegroundColor Cyan
Write-Host "1. Go to Issues → Search for issues in JIRA" -ForegroundColor White
Write-Host "2. Build your search using the UI or Advanced Search (JQL)" -ForegroundColor White
Write-Host "3. Copy the URL from your browser's address bar" -ForegroundColor White
Write-Host "4. Paste it as the -JqlQuery or -LinkToJql parameter" -ForegroundColor White
Write-Host ""

Write-Host "Supported URL parameters:" -ForegroundColor Cyan
Write-Host "- jql=<encoded-query>" -ForegroundColor White
Write-Host "- jqlQuery=<encoded-query>" -ForegroundColor White
Write-Host ""

Write-Host "Note: The script automatically detects whether you're providing a URL or JQL query" -ForegroundColor Yellow
Write-Host "and handles the parsing accordingly." -ForegroundColor Yellow
