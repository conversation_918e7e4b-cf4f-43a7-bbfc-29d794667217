# Test-SSLBypass.ps1
# Test script to demonstrate SSL bypass functionality

Write-Host "SSL Bypass Test for SYSTEM Account" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Current user context:" -ForegroundColor Yellow
Write-Host "User: $env:USERNAME" -ForegroundColor White
Write-Host "Domain: $env:USERDOMAIN" -ForegroundColor White
Write-Host ""

# Test SSL configuration
Write-Host "Testing SSL configuration..." -ForegroundColor Cyan

# Show current SSL settings
Write-Host "Current SecurityProtocol: $([System.Net.ServicePointManager]::SecurityProtocol)" -ForegroundColor White
Write-Host "Current ServerCertificateValidationCallback: $([System.Net.ServicePointManager]::ServerCertificateValidationCallback)" -ForegroundColor White
Write-Host ""

# Test SSL bypass setup
Write-Host "Setting up SSL bypass..." -ForegroundColor Yellow
try {
    # Bypass SSL certificate validation
    [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12 -bor [System.Net.SecurityProtocolType]::Tls11 -bor [System.Net.SecurityProtocolType]::Tls
    Write-Host "✓ SSL certificate validation bypassed successfully" -ForegroundColor Green
    Write-Host "✓ Security protocols configured: $([System.Net.ServicePointManager]::SecurityProtocol)" -ForegroundColor Green
}
catch {
    Write-Host "✗ Error configuring SSL bypass: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

Write-Host "Your command with SSL bypass should be:" -ForegroundColor Cyan
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"MDk1MjE3MzQ4Mzk2Oqjf5wW+fjTv1y7MefxSo4/qpNxT`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?filter=77917`" \`" -ForegroundColor Green
Write-Host "    -AssigneeUsername `"jimle`" \`" -ForegroundColor Green
Write-Host "    -Label `"no-auto-assign`" \`" -ForegroundColor Green
Write-Host "    -MaxResults 1000 \`" -ForegroundColor Green
Write-Host "    -BypassSSL" -ForegroundColor Green
Write-Host ""

Write-Host "Common SSL/TLS errors that this fixes:" -ForegroundColor Yellow
Write-Host "- 'Could not establish trust relationship for the SSL/TLS secure channel'" -ForegroundColor White
Write-Host "- 'The underlying connection was closed: Could not establish trust relationship'" -ForegroundColor White
Write-Host "- 'The remote certificate is invalid according to the validation procedure'" -ForegroundColor White
Write-Host "- Certificate chain errors when running as SYSTEM account" -ForegroundColor White
Write-Host ""

Write-Host "When to use -BypassSSL:" -ForegroundColor Yellow
Write-Host "- Running as SYSTEM account (common in scheduled tasks)" -ForegroundColor White
Write-Host "- Self-signed certificates on JIRA server" -ForegroundColor White
Write-Host "- Corporate proxy/firewall intercepting SSL" -ForegroundColor White
Write-Host "- Non-domain joined machines" -ForegroundColor White
Write-Host ""

Write-Host "Security note:" -ForegroundColor Red
Write-Host "The -BypassSSL parameter disables SSL certificate validation." -ForegroundColor Yellow
Write-Host "Only use this in trusted network environments." -ForegroundColor Yellow
