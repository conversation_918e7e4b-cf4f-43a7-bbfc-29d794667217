# Test-FilterUrl.ps1
# Test script to verify filter URL parsing

# Load System.Web assembly for URL parsing
Add-Type -AssemblyName System.Web

# Function to extract filter ID from URL (simplified version for testing)
function Test-FilterUrlParsing {
    param (
        [string]$UrlOrJql
    )
    
    Write-Host "Testing URL: $UrlOrJql" -ForegroundColor Cyan
    
    # If it's not a URL (doesn't start with http), return as-is (assume it's JQL)
    if (-not $UrlOrJql.StartsWith("http")) {
        Write-Host "Input appears to be JQL query (not URL): $UrlOrJql" -ForegroundColor Green
        return $UrlOrJql
    }
    
    try {
        # Parse the URL
        $uri = [System.Uri]$UrlOrJql
        Write-Host "Parsed URI: $($uri.ToString())" -ForegroundColor White
        
        # Extract query parameters
        $queryParams = [System.Web.HttpUtility]::ParseQueryString($uri.Query)
        Write-Host "Query parameters found:" -ForegroundColor White
        foreach ($key in $queryParams.AllKeys) {
            Write-Host "  $key = $($queryParams[$key])" -ForegroundColor Gray
        }
        
        # Look for JQL parameter first
        $jql = $queryParams["jql"]
        if ([string]::IsNullOrEmpty($jql)) {
            $jql = $queryParams["jqlQuery"]
        }
        
        # If no JQL found, check for filter parameter
        if ([string]::IsNullOrEmpty($jql)) {
            $filterId = $queryParams["filter"]
            if (-not [string]::IsNullOrEmpty($filterId)) {
                Write-Host "Found filter ID: $filterId" -ForegroundColor Green
                Write-Host "In the real script, this would call the JIRA API to get the filter's JQL" -ForegroundColor Yellow
                return "FILTER_ID:$filterId"
            }
        } else {
            # URL decode the JQL
            $decodedJql = [System.Web.HttpUtility]::UrlDecode($jql)
            Write-Host "Extracted JQL from URL: $decodedJql" -ForegroundColor Green
            return $decodedJql
        }
        
        Write-Host "Warning: No JQL or filter parameter found in URL" -ForegroundColor Yellow
        return $null
    }
    catch {
        Write-Host "Error parsing URL: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Test your specific URL
$testUrl = "https://jira.softlayer.local/issues/?filter=77917"
Write-Host "Testing your specific filter URL:" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
$result = Test-FilterUrlParsing -UrlOrJql $testUrl
Write-Host "Result: $result" -ForegroundColor White
Write-Host ""

# Test other URL types for comparison
Write-Host "Testing other URL types for comparison:" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

$otherTests = @(
    "https://jira.softlayer.local/issues/?jql=project%20%3D%20PROJ",
    "project = PROJ AND status = 'To Do'",
    "https://jira.softlayer.local/browse/PROJ-123"
)

foreach ($test in $otherTests) {
    Write-Host ""
    $result = Test-FilterUrlParsing -UrlOrJql $test
    Write-Host "Result: $result" -ForegroundColor White
}
