# Example-SystemAccount.ps1
# Example for running Assign-JiraTickets.ps1 as SYSTEM account with SSL bypass

Write-Host "Running JIRA Scripts as SYSTEM Account" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Problem:" -ForegroundColor Red
Write-Host "When running PowerShell scripts as SYSTEM account (common in scheduled tasks)," -ForegroundColor White
Write-Host "you may encounter SSL/TLS certificate validation errors like:" -ForegroundColor White
Write-Host "- 'Could not establish trust relationship for the SSL/TLS secure channel'" -ForegroundColor Yellow
Write-Host "- 'The underlying connection was closed: Could not establish trust relationship'" -ForegroundColor Yellow
Write-Host ""

Write-Host "Solution:" -ForegroundColor Green
Write-Host "Add the -BypassSSL parameter to your command" -ForegroundColor White
Write-Host ""

Write-Host "Your Original Command (that failed):" -ForegroundColor Red
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Red
Write-Host "    -PersonalAccessToken `"MDk1MjE3MzQ4Mzk2Oqjf5wW+fjTv1y7MefxSo4/qpNxT`" \`" -ForegroundColor Red
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?filter=77917`" \`" -ForegroundColor Red
Write-Host "    -AssigneeUsername `"jimle`" \`" -ForegroundColor Red
Write-Host "    -Label `"no-auto-assign`" \`" -ForegroundColor Red
Write-Host "    -MaxResults 1000" -ForegroundColor Red
Write-Host ""

Write-Host "Your Fixed Command (with SSL bypass):" -ForegroundColor Green
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"MDk1MjE3MzQ4Mzk2Oqjf5wW+fjTv1y7MefxSo4/qpNxT`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?filter=77917`" \`" -ForegroundColor Green
Write-Host "    -AssigneeUsername `"jimle`" \`" -ForegroundColor Green
Write-Host "    -Label `"no-auto-assign`" \`" -ForegroundColor Green
Write-Host "    -MaxResults 1000 \`" -ForegroundColor Green
Write-Host "    -BypassSSL" -ForegroundColor Green
Write-Host ""

Write-Host "What the -BypassSSL parameter does:" -ForegroundColor Cyan
Write-Host "1. Disables SSL certificate validation" -ForegroundColor White
Write-Host "2. Configures TLS 1.2, 1.1, and 1.0 support" -ForegroundColor White
Write-Host "3. Allows connections to self-signed certificate servers" -ForegroundColor White
Write-Host "4. Resolves SYSTEM account certificate trust issues" -ForegroundColor White
Write-Host ""

Write-Host "When to use -BypassSSL:" -ForegroundColor Yellow
Write-Host "✓ Running as SYSTEM account in scheduled tasks" -ForegroundColor White
Write-Host "✓ JIRA server uses self-signed certificates" -ForegroundColor White
Write-Host "✓ Corporate proxy/firewall intercepting SSL traffic" -ForegroundColor White
Write-Host "✓ Non-domain joined machines" -ForegroundColor White
Write-Host "✓ Development/testing environments" -ForegroundColor White
Write-Host ""

Write-Host "Security Considerations:" -ForegroundColor Red
Write-Host "- Only use -BypassSSL in trusted network environments" -ForegroundColor Yellow
Write-Host "- The parameter disables certificate validation" -ForegroundColor Yellow
Write-Host "- Consider using proper certificates in production" -ForegroundColor Yellow
Write-Host ""

Write-Host "For Scheduled Tasks:" -ForegroundColor Cyan
Write-Host "When creating a scheduled task to run this script:" -ForegroundColor White
Write-Host "1. Set 'Run whether user is logged on or not'" -ForegroundColor White
Write-Host "2. Set 'Run with highest privileges'" -ForegroundColor White
Write-Host "3. Use SYSTEM account or a service account" -ForegroundColor White
Write-Host "4. Always include -BypassSSL parameter" -ForegroundColor White
Write-Host "5. Consider adding -Force to skip confirmations" -ForegroundColor White
