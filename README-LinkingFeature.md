# Jira Ticket Linking Enhancement

This document describes the new issue linking functionality added to `Assign-JiraTickets.ps1`.

## Important Change: Assignment is Now Optional!

The `-AssigneeUsername` parameter is now **optional**. You can use the script for:
- **Assignment only**: Specify `-AssigneeUsername` without other parameters
- **Linking only**: Specify `-LinkToJql` without `-AssigneeUsername`
- **Labeling only**: Specify `-Label` without other parameters
- **Sprint only**: Specify `-SprintName` without other parameters
- **Combined operations**: Any combination of assignment, labeling, linking, and sprint operations

**Requirement**: At least one operation must be specified (assignment, labeling, linking, or sprint).

## New Parameters

### `-LinkToJql`
- **Type**: String
- **Description**: JQL query string or JIRA URL containing jql parameter to find issues that should be linked to the tickets being processed
- **JQL Examples**: `"key = 'PROJ-123'"` or `"labels = 'dependency'"`
- **URL Examples**: `"https://jira.softlayer.local/issues/?jql=key%20%3D%20%27PROJ-123%27"` or `"https://jira.softlayer.local/issues/?jql=labels%20%3D%20dependency"`

### `-LinkType`
- **Type**: String
- **Default**: "Relates"
- **Description**: The type of link to create between issues
- **Common Values**: "Relates", "Blocks", "Depends", "Duplicates", "Clones"

### `-LinkComment`
- **Type**: String
- **Description**: Optional comment to add when creating the link
- **Example**: "Automatically linked by assignment script"

### `-LinkAsInward`
- **Type**: Switch
- **Description**: If specified, creates inward links (target issues -> current issues)
- **Default**: Creates outward links (current issues -> target issues)

### `-SprintId`
- **Type**: Integer
- **Description**: ID of the sprint to add issues to (much faster than name search)
- **Example**: `123` or `456`
- **How to find**: Sprint ID can be found in Jira URLs or via API calls

### `-Force`
- **Type**: Switch
- **Description**: Skip confirmation prompts for automated operations
- **Usage**: Useful for scripts and automation where user interaction is not desired

### `-BypassSSL`
- **Type**: Switch
- **Description**: Bypass SSL certificate validation
- **Usage**: Required when running as SYSTEM account or with self-signed certificates
- **Common scenarios**: Scheduled tasks, non-domain machines, corporate proxies

## How It Works

1. **Process Each Issue**: For each issue being assigned, the script:
   - Assigns the issue to the specified user
   - Adds the specified label (if provided)
   - Creates links to issues found by the LinkToJql query (if provided)

2. **Link Direction**:
   - **Outward Links** (default): Current issue -> Target issues
   - **Inward Links** (-LinkAsInward): Target issues -> Current issue

3. **Link Creation**: Uses Jira REST API `/rest/api/2/issueLink` endpoint to create links

## JIRA URL Support

The script now supports both JQL query strings and JIRA URLs for the `-JqlQuery` and `-LinkToJql` parameters.

### Supported URL Formats
- **Issue Navigator URLs**: `https://jira.softlayer.local/issues/?jql=<encoded-jql>`
- **Search URLs**: `https://jira.softlayer.local/secure/IssueNavigator.jspa?jql=<encoded-jql>`
- **Filter URLs**: `https://jira.softlayer.local/issues/?filter=<filter-id>`

### How URL Parsing Works
1. The script detects if the input starts with "http" (indicating a URL)
2. Extracts the `jql`, `jqlQuery`, or `filter` parameter from the URL
3. If it's a filter ID, retrieves the JQL from the JIRA filter via API
4. URL-decodes the JQL query (if needed)
5. Uses the decoded JQL for the search

### Benefits
- **Copy-paste from browser**: Simply copy the URL from your JIRA search and paste it as the parameter
- **No manual URL decoding**: The script automatically handles URL encoding/decoding
- **Backward compatibility**: Existing JQL queries continue to work unchanged

### Getting JIRA URLs
1. **From Issue Navigator**:
   - Go to Issues → Search for issues in JIRA
   - Build your search using the UI or Advanced Search (JQL)
   - Copy the URL from your browser's address bar

2. **From Saved Filters**:
   - Open a saved filter in JIRA
   - Copy the URL from the browser address bar

3. **URL Format**: The URL should contain a `jql=` parameter with the encoded query

## Usage Examples

### Assignment with Linking (JQL)
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "assignee = 'old.user'" `
    -AssigneeUsername "new.user" `
    -LinkToJql "key = 'PARENT-123'" `
    -LinkType "Relates"
```

### Assignment with Linking (JIRA URLs)
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "https://jira.softlayer.local/issues/?jql=assignee%20%3D%20%27old.user%27" `
    -AssigneeUsername "new.user" `
    -LinkToJql "https://jira.softlayer.local/issues/?jql=key%20%3D%20%27PARENT-123%27" `
    -LinkType "Relates"
```

### Link-Only Operation (No Assignment)
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "project = 'PROJ' AND type = Story" `
    -LinkToJql "project = 'PROJ' AND type = Epic" `
    -LinkType "Relates"
```

### Label-Only Operation (with URL)
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "https://jira.softlayer.local/issues/?jql=status%20%3D%20%27In%20Review%27" `
    -Label "reviewed"
```

### Sprint-Only Operation (with URL)
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "https://jira.softlayer.local/issues/?jql=project%20%3D%20%27PROJ%27%20AND%20status%20%3D%20%27Ready%27" `
    -SprintId 123
```

### Using Saved Filter URL
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "https://jira.softlayer.local/issues/?filter=77917" `
    -AssigneeUsername "developer" `
    -Label "assigned-from-filter"
```

### Automated Operation (No Confirmation)
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "assignee is EMPTY" `
    -AssigneeUsername "developer" `
    -SprintId 456 `
    -Force
```

### SYSTEM Account / SSL Issues
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "https://jira.softlayer.local/issues/?filter=77917" `
    -AssigneeUsername "developer" `
    -Label "auto-assigned" `
    -BypassSSL `
    -Force
```

### Combined Operations
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "project = 'PROJ'" `
    -AssigneeUsername "developer" `
    -Label "processed" `
    -LinkToJql "key = 'EPIC-123'" `
    -SprintId 789 `
    -Force
```

### Link to Epic Issues
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JiraIssueKey "TASK-456" `
    -AssigneeUsername "developer" `
    -LinkToJql "cf[10014] = 'EPIC-789'" `
    -LinkType "Relates" `
    -LinkComment "Part of epic coordination"
```

### Create Blocking Relationships
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "your-token" `
    -JqlQuery "type = Bug AND priority = High" `
    -AssigneeUsername "bug.fixer" `
    -LinkToJql "priority = Critical" `
    -LinkType "Blocks" `
    -LinkAsInward
```

## Rate Limiting

The script includes rate limiting to avoid overwhelming the Jira API:
- 500ms delay between link creation calls
- 2-3 second delays between processing different issues

## Error Handling

- Links to self are automatically skipped
- Failed link creation is logged but doesn't stop processing
- Authentication is tested before any operations begin

## Limitations

- Cannot create multiple different link types in a single run
- Link creation is limited to 50 target issues per source issue
- Requires appropriate Jira permissions to create issue links

## Troubleshooting

1. **Authentication Errors**: Ensure your Personal Access Token has sufficient permissions
2. **Link Type Errors**: Verify the link type exists in your Jira configuration
3. **JQL Errors**: Test your JQL queries in Jira's issue search before using in the script
4. **Permission Errors**: Ensure you have permission to link issues in the relevant projects
