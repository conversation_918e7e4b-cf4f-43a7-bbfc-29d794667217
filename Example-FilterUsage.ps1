# Example-FilterUsage.ps1
# Example showing how to use JIRA filter URLs with Assign-JiraTickets.ps1

Write-Host "JIRA Filter URL Usage Example" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Your command (now supported!):" -ForegroundColor Yellow
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"MDk1MjE3MzQ4Mzk2Oqjf5wW+fjTv1y7MefxSo4/qpNxT`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?filter=77917`" \`" -ForegroundColor Green
Write-Host "    -AssigneeUsername `"jimle`" \`" -ForegroundColor Green
Write-Host "    -Label `"no-auto-assign`" \`" -ForegroundColor Green
Write-Host "    -MaxResults 1000" -ForegroundColor Green
Write-Host ""

Write-Host "What happens when you run this:" -ForegroundColor Cyan
Write-Host "1. Script detects the filter URL format" -ForegroundColor White
Write-Host "2. Extracts filter ID: 77917" -ForegroundColor White
Write-Host "3. Calls JIRA API: GET /rest/api/2/filter/77917" -ForegroundColor White
Write-Host "4. Retrieves the JQL query from the filter" -ForegroundColor White
Write-Host "5. Uses that JQL to search for issues" -ForegroundColor White
Write-Host "6. Assigns found issues to 'jimle'" -ForegroundColor White
Write-Host "7. Adds 'no-auto-assign' label to each issue" -ForegroundColor White
Write-Host ""

Write-Host "Other filter URL examples:" -ForegroundColor Cyan
Write-Host ""

Write-Host "Example 1: Just add labels to filter results" -ForegroundColor Yellow
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"your-token`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?filter=12345`" \`" -ForegroundColor Green
Write-Host "    -Label `"processed`"" -ForegroundColor Green
Write-Host ""

Write-Host "Example 2: Link filter results to an epic" -ForegroundColor Yellow
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"your-token`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?filter=67890`" \`" -ForegroundColor Green
Write-Host "    -LinkToJql `"key = 'EPIC-456'`" \`" -ForegroundColor Green
Write-Host "    -LinkType `"Relates`"" -ForegroundColor Green
Write-Host ""

Write-Host "Example 3: Add filter results to a sprint" -ForegroundColor Yellow
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"your-token`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?filter=11111`" \`" -ForegroundColor Green
Write-Host "    -SprintId 789" -ForegroundColor Green
Write-Host ""

Write-Host "How to get filter URLs:" -ForegroundColor Cyan
Write-Host "1. Go to Issues → Manage filters in JIRA" -ForegroundColor White
Write-Host "2. Click on a saved filter" -ForegroundColor White
Write-Host "3. Copy the URL from your browser (it will contain ?filter=XXXXX)" -ForegroundColor White
Write-Host "4. Use that URL directly in the script" -ForegroundColor White
Write-Host ""

Write-Host "Benefits of using filter URLs:" -ForegroundColor Cyan
Write-Host "- No need to remember complex JQL queries" -ForegroundColor White
Write-Host "- Leverage existing saved filters" -ForegroundColor White
Write-Host "- Easy to share filter-based operations with team members" -ForegroundColor White
Write-Host "- Filters can be updated in JIRA UI without changing scripts" -ForegroundColor White
