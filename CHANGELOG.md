# Changelog - Jira Ticket Management Enhancement

## Version 4.0 - JIRA URL Support

### 🎉 New Features Added

#### JIRA URL Parsing
- **Enhanced Parameters**: `-JqlQuery` and `-LinkToJql` now accept JIRA URLs in addition to JQL queries
- **Automatic URL Parsing**: Extracts JQL from JIRA Issue Navigator URLs and Filter URLs
- **Filter Support**: Automatically retrieves JQL from saved JIRA filters via API
- **URL Decoding**: Automatically handles URL-encoded JQL queries
- **Copy-Paste Workflow**: Simply copy URLs from your browser and use them directly

#### Supported URL Formats
- Issue Navigator: `https://jira.softlayer.local/issues/?jql=<encoded-jql>`
- Search URLs: `https://jira.softlayer.local/secure/IssueNavigator.jspa?jql=<encoded-jql>`
- Filter URLs: `https://jira.softlayer.local/issues/?filter=<filter-id>`

#### SSL/SYSTEM Account Support
- **New Parameter**: `-BypassSSL` - Bypass SSL certificate validation
- **Enhanced Error Handling**: Detects SSL/TLS errors and provides helpful guidance
- **SYSTEM Account Compatible**: Resolves common SSL issues when running as SYSTEM
- **Security Protocols**: Configures TLS 1.2, 1.1, and 1.0 support

#### New Functions
- `Get-JqlFromUrl()` - Extracts and decodes JQL from JIRA URLs or retrieves JQL from filter IDs
- `Get-JqlFromFilter()` - Retrieves JQL query from JIRA filter via REST API
- Enhanced `Test-JiraAuth()` - Provides SSL troubleshooting guidance
- Backward compatibility maintained for existing JQL query usage

## Version 3.0 - Sprint Management and Automation

### 🎉 New Features Added

#### Sprint Management
- **New Parameter**: `-SprintName` - Add issues to sprints by name
- **Sprint Lookup**: Automatically finds sprints across all boards
- **Sprint Validation**: Verifies sprint exists before processing issues

#### Automation Support
- **New Parameter**: `-Force` - Skip confirmation prompts
- **Automated Workflows**: Perfect for CI/CD and scripted operations
- **Batch Processing**: Process hundreds of tickets without manual intervention

## Version 2.0 - Issue Linking and Optional Assignment

### 🎉 Major New Features

#### Issue Linking via JQL
- **New Parameter**: `-LinkToJql` - JQL query to find issues to link to
- **New Parameter**: `-LinkType` - Type of link to create (default: "Relates")
- **New Parameter**: `-LinkComment` - Optional comment for links
- **New Parameter**: `-LinkAsInward` - Switch to create inward links

#### Flexible Operations
- **Assignment is now OPTIONAL** - `-AssigneeUsername` is no longer required
- **Link-only operations** - Create links without changing assignments
- **Label-only operations** - Add labels without assignments or links
- **Combined operations** - Any combination of assignment, labeling, and linking

### 🔧 Technical Improvements

#### New Functions
- `Get-IssuesFromJql()` - Retrieves issues using JQL with pagination
- `Add-IssueLink()` - Creates individual issue links via REST API
- `Add-LinksToIssue()` - Handles linking multiple target issues to source

#### Enhanced Validation
- Validates that at least one operation is specified
- Prevents self-linking (issue linking to itself)
- Improved error handling for link creation failures

#### Rate Limiting
- 500ms delay between link creation calls
- Maintains existing 2-3 second delays between issue processing

### 📝 Usage Examples

#### Link-Only Operation
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "token" `
    -JqlQuery "project = 'PROJ'" `
    -LinkToJql "key = 'EPIC-123'" `
    -LinkType "Relates"
```

#### Label-Only Operation
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "token" `
    -JqlQuery "status = 'Done'" `
    -Label "completed"
```

#### Sprint-Only Operation
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "token" `
    -JqlQuery "status = 'Ready for Development'" `
    -SprintName "Sprint 2024-01" `
    -Force
```

#### Combined Operations with Automation
```powershell
.\Assign-JiraTickets.ps1 `
    -PersonalAccessToken "token" `
    -JqlQuery "assignee = 'old.user'" `
    -AssigneeUsername "new.user" `
    -Label "reassigned" `
    -LinkToJql "key = 'PARENT-456'" `
    -SprintName "Current Sprint" `
    -Force
```

### 🔄 Backward Compatibility
- All existing functionality preserved
- Existing scripts will continue to work unchanged
- Parameter syntax remains the same for assignment operations

### 📚 Documentation
- `README-LinkingFeature.md` - Comprehensive feature documentation
- `Example-LinkTickets.ps1` - Practical usage examples
- Enhanced inline documentation and comments

### 🚀 Benefits
- **Flexibility**: Perform any combination of operations
- **Efficiency**: Batch link creation with rate limiting
- **Automation**: Link tickets based on dynamic JQL queries
- **Integration**: Works with existing Jira workflows and permissions

### ⚠️ Requirements
- Jira Personal Access Token with link creation permissions
- At least one operation must be specified (validation enforced)
- Link types must exist in your Jira configuration
