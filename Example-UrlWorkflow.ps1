# Example-UrlWorkflow.ps1
# Real-world workflow examples using JIRA URLs with the enhanced Assign-JiraTickets.ps1

# Replace with your actual Personal Access Token
$token = "your-personal-access-token-here"

Write-Host "Real-World JIRA URL Workflow Examples" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Scenario 1: Assign unassigned tickets from a saved filter" -ForegroundColor Yellow
Write-Host "Steps:" -ForegroundColor White
Write-Host "1. Go to JIRA and create/open a filter for unassigned tickets" -ForegroundColor White
Write-Host "2. Copy the URL from your browser" -ForegroundColor White
Write-Host "3. Use the URL directly in the script" -ForegroundColor White
Write-Host ""
Write-Host "Command:" -ForegroundColor Green
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"$token`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?jql=assignee%20is%20EMPTY%20AND%20project%20%3D%20MYPROJ%20AND%20status%20%3D%20%27To%20Do%27`" \`" -ForegroundColor Green
Write-Host "    -AssigneeUsername `"developer.name`"" -ForegroundColor Green
Write-Host ""

Write-Host "Scenario 2: Link stories to an epic using URLs" -ForegroundColor Yellow
Write-Host "Steps:" -ForegroundColor White
Write-Host "1. Search for stories in JIRA, copy the URL" -ForegroundColor White
Write-Host "2. Search for the epic, copy that URL too" -ForegroundColor White
Write-Host "3. Use both URLs in the script" -ForegroundColor White
Write-Host ""
Write-Host "Command:" -ForegroundColor Green
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"$token`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?jql=project%20%3D%20MYPROJ%20AND%20type%20%3D%20Story%20AND%20status%20%3D%20%27To%20Do%27`" \`" -ForegroundColor Green
Write-Host "    -LinkToJql `"https://jira.softlayer.local/issues/?jql=key%20%3D%20%27EPIC-123%27`" \`" -ForegroundColor Green
Write-Host "    -LinkType `"Relates`"" -ForegroundColor Green
Write-Host ""

Write-Host "Scenario 3: Add labels to tickets from a complex search" -ForegroundColor Yellow
Write-Host "Steps:" -ForegroundColor White
Write-Host "1. Use JIRA's advanced search to build a complex query" -ForegroundColor White
Write-Host "2. Copy the resulting URL" -ForegroundColor White
Write-Host "3. Use it to add labels to all matching tickets" -ForegroundColor White
Write-Host ""
Write-Host "Command:" -ForegroundColor Green
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"$token`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?jql=project%20%3D%20MYPROJ%20AND%20status%20%3D%20%27In%20Review%27%20AND%20assignee%20%3D%20currentUser%28%29`" \`" -ForegroundColor Green
Write-Host "    -Label `"ready-for-testing`"" -ForegroundColor Green
Write-Host ""

Write-Host "Scenario 4: Bulk operations with automation" -ForegroundColor Yellow
Write-Host "Perfect for CI/CD pipelines or scheduled tasks:" -ForegroundColor White
Write-Host ""
Write-Host "Command:" -ForegroundColor Green
Write-Host ".\Assign-JiraTickets.ps1 \`" -ForegroundColor Green
Write-Host "    -PersonalAccessToken `"$token`" \`" -ForegroundColor Green
Write-Host "    -JqlQuery `"https://jira.softlayer.local/issues/?jql=status%20%3D%20%27Ready%20for%20Development%27%20AND%20assignee%20is%20EMPTY`" \`" -ForegroundColor Green
Write-Host "    -AssigneeUsername `"auto.developer`" \`" -ForegroundColor Green
Write-Host "    -Label `"auto-assigned`" \`" -ForegroundColor Green
Write-Host "    -SprintId 456 \`" -ForegroundColor Green
Write-Host "    -Force" -ForegroundColor Green
Write-Host ""

Write-Host "Tips for getting JIRA URLs:" -ForegroundColor Cyan
Write-Host "1. Navigate to Issues → Search for issues" -ForegroundColor White
Write-Host "2. Use the Basic or Advanced search to find your tickets" -ForegroundColor White
Write-Host "3. Copy the URL from your browser's address bar" -ForegroundColor White
Write-Host "4. The URL will contain the encoded JQL query" -ForegroundColor White
Write-Host ""

Write-Host "Common URL patterns:" -ForegroundColor Cyan
Write-Host "- %20 = space" -ForegroundColor White
Write-Host "- %3D = equals (=)" -ForegroundColor White
Write-Host "- %27 = single quote (')" -ForegroundColor White
Write-Host "- %22 = double quote (`")" -ForegroundColor White
Write-Host "- %28 = opening parenthesis ((" -ForegroundColor White
Write-Host "- %29 = closing parenthesis ())" -ForegroundColor White
