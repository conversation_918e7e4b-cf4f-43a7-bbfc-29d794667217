# Test-JiraUrlParsing.ps1
# Test script to demonstrate URL parsing functionality for JIRA URLs

# Load System.Web assembly for URL parsing
Add-Type -AssemblyName System.Web

# Function to extract JQL from JIRA URL
function Get-JqlFromUrl {
    param (
        [string]$UrlOrJql
    )
    
    # If it's not a URL (doesn't start with http), return as-is (assume it's JQL)
    if (-not $UrlOrJql.StartsWith("http")) {
        Write-Host "Input appears to be JQL query (not URL): $UrlOrJql" -ForegroundColor Green
        return $UrlOrJql
    }
    
    try {
        # Parse the URL
        $uri = [System.Uri]$UrlOrJql
        
        # Extract query parameters
        $queryParams = [System.Web.HttpUtility]::ParseQueryString($uri.Query)
        
        # Look for JQL parameter (common parameter names: jql, jqlQuery)
        $jql = $queryParams["jql"]
        if ([string]::IsNullOrEmpty($jql)) {
            $jql = $queryParams["jqlQuery"]
        }
        
        if ([string]::IsNullOrEmpty($jql)) {
            Write-Host "Warning: No JQL parameter found in URL. Supported parameters: jql, jqlQuery" -ForegroundColor Yellow
            Write-Host "URL: $UrlOrJql" -ForegroundColor Yellow
            return $null
        }
        
        # URL decode the JQL
        $decodedJql = [System.Web.HttpUtility]::UrlDecode($jql)
        Write-Host "Extracted JQL from URL: $decodedJql" -ForegroundColor Green
        return $decodedJql
    }
    catch {
        Write-Host "Error parsing URL: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "URL: $UrlOrJql" -ForegroundColor Red
        return $null
    }
}

Write-Host "Testing JIRA URL parsing functionality" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Test cases
$testCases = @(
    # Regular JQL query
    "project = PROJ AND status = 'To Do'",
    
    # JIRA URL with jql parameter
    "https://jira.softlayer.local/issues/?jql=project%20%3D%20PROJ%20AND%20status%20%3D%20%27To%20Do%27",
    
    # JIRA URL with simple query
    "https://jira.softlayer.local/issues/?jql=assignee%20is%20EMPTY",
    
    # JIRA URL with complex query
    "https://jira.softlayer.local/issues/?jql=project%20%3D%20%22My%20Project%22%20AND%20labels%20in%20%28bug%2C%20critical%29",
    
    # URL without jql parameter (should fail)
    "https://jira.softlayer.local/browse/PROJ-123",
    
    # Invalid URL (should fail)
    "not-a-valid-url"
)

foreach ($testCase in $testCases) {
    Write-Host "`nTesting: $testCase" -ForegroundColor Yellow
    Write-Host "Result:" -ForegroundColor White
    $result = Get-JqlFromUrl -UrlOrJql $testCase
    if ($null -ne $result) {
        Write-Host "  JQL: $result" -ForegroundColor White
    } else {
        Write-Host "  Failed to extract JQL" -ForegroundColor Red
    }
    Write-Host ""
}

Write-Host "Test completed!" -ForegroundColor Green
