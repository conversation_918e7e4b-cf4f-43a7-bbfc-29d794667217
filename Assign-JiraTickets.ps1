# Assign-JiraTickets.ps1
# Script to assist with assigning Jira tickets to users and creating issue links
#
# Features:
# - Assign single tickets or tickets found via JQL query or JIRA URL (optional)
# - Add labels to tickets (optional)
# - Create issue links between tickets using JQL queries or JIRA URLs (optional)
# - Add tickets to sprints (optional)
# - Skip confirmation prompts with -Force switch
# - At least one operation must be specified
#
# JQL Query Examples:
# - Search by JQL: -JqlQuery "project = PROJ AND status = 'To Do'"
# - Search by JIRA URL: -JqlQuery "https://jira.softlayer.local/issues/?jql=project%20%3D%20PROJ%20AND%20status%20%3D%20%27To%20Do%27"
# - Search by Filter URL: -JqlQuery "https://jira.softlayer.local/issues/?filter=12345"
#
# Issue Linking Examples:
# - Link all found issues to a specific ticket: -LinkToJql "key = 'PROJ-123'"
# - Link using JIRA URL: -LinkToJql "https://jira.softlayer.local/issues/?jql=key%20%3D%20%27PROJ-123%27"
# - Link to all issues in a specific epic: -LinkToJql "cf[10014] = 'PROJ-456'"
# - Link to all issues with specific labels: -LinkToJql "labels in ('bug', 'critical')"
# - Create inward links (target -> current): -LinkAsInward
# - Specify link type: -LinkType "Blocks" (default: "Relates")
# - Add comment to links: -LinkComment "Automatically linked by script"
#
# Sprint Examples:
# - Add tickets to sprint: -SprintId 123
# - Combined operations: -AssigneeUsername "user" -SprintId 456 -Label "assigned"
#
# URL Examples:
# - Mixed URL and JQL: -JqlQuery "https://jira.softlayer.local/issues/?jql=project%20%3D%20PROJ" -LinkToJql "key = 'EPIC-123'"
# - Both URLs: -JqlQuery "https://jira.softlayer.local/issues/?jql=assignee%20is%20EMPTY" -LinkToJql "https://jira.softlayer.local/issues/?jql=key%20%3D%20%27EPIC-123%27"
#
# Automation Examples:
# - Skip confirmation: -Force
# - Link-only with no prompts: -JqlQuery "project = PROJ" -LinkToJql "key = 'EPIC-123'" -Force
# - Add to sprint only: -JqlQuery "status = Ready" -SprintId 789 -Force
#
# SSL/System Account Examples:
# - Bypass SSL for SYSTEM account: -BypassSSL
# - Full command with SSL bypass: -JqlQuery "project = PROJ" -AssigneeUsername "user" -BypassSSL

param(
    [Parameter(Mandatory=$true)]
    [string]$PersonalAccessToken,

    [Parameter(ParameterSetName="SingleTicket", Mandatory=$true)]
    [string]$JiraIssueKey,

    [Parameter(ParameterSetName="SearchFilter", Mandatory=$true)]
    [string]$JqlQuery,  # JQL query string, JIRA URL containing jql parameter, or JIRA filter URL

    [Parameter()]
    [string]$AssigneeUsername,

    [Parameter(ParameterSetName="SearchFilter")]
    [int]$MaxResults = 1000,  # Increased default from 50 to 1000

    [Parameter()]
    [string]$Label,

    # Issue linking parameters
    [Parameter()]
    [string]$LinkToJql,  # JQL query string, JIRA URL containing jql parameter, or JIRA filter URL to find issues to link to

    [Parameter()]
    [string]$LinkType = "Relates",  # Default link type

    [Parameter()]
    [string]$LinkComment,  # Optional comment for the link

    [Parameter()]
    [switch]$LinkAsInward,  # If specified, creates inward links (current issue <- target issues), otherwise outward (current issue -> target issues)

    # Sprint parameter
    [Parameter()]
    [int]$SprintId,  # ID of the sprint to add issues to

    # Automation parameter
    [Parameter()]
    [switch]$Force,  # Skip confirmation prompts

    # SSL bypass parameter
    [Parameter()]
    [switch]$BypassSSL  # Bypass SSL certificate validation (useful for SYSTEM account or self-signed certs)
)

# Validate that at least one operation is specified
if ([string]::IsNullOrEmpty($AssigneeUsername) -and [string]::IsNullOrEmpty($Label) -and [string]::IsNullOrEmpty($LinkToJql) -and ($SprintId -eq 0)) {
    Write-Host "Error: You must specify at least one operation:" -ForegroundColor Red
    Write-Host "  -AssigneeUsername (to assign tickets)" -ForegroundColor Red
    Write-Host "  -Label (to add labels)" -ForegroundColor Red
    Write-Host "  -LinkToJql (to create links)" -ForegroundColor Red
    Write-Host "  -SprintId (to add to sprint)" -ForegroundColor Red
    exit 1
}

# Hardcoded Jira URL
$JiraUrl = "https://jira.softlayer.local"

# Load System.Web assembly for URL parsing
Add-Type -AssemblyName System.Web

# SSL Certificate bypass for SYSTEM account and self-signed certificates
# This is commonly needed when running as SYSTEM account on non-domain machines
if ($BypassSSL.IsPresent) {
    try {
        # Bypass SSL certificate validation
        [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12 -bor [System.Net.SecurityProtocolType]::Tls11 -bor [System.Net.SecurityProtocolType]::Tls
        Write-Host "SSL certificate validation bypassed (useful for SYSTEM account or self-signed certificates)" -ForegroundColor Yellow
    }
    catch {
        Write-Host "Warning: Could not configure SSL bypass: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Create auth header with Bearer token
$headers = @{
    Authorization = "Bearer $PersonalAccessToken"
    "Content-Type" = "application/json"
}

# Function to extract JQL from JIRA URL or get JQL from filter ID
function Get-JqlFromUrl {
    param (
        [string]$UrlOrJql
    )

    # If it's not a URL (doesn't start with http), return as-is (assume it's JQL)
    if (-not $UrlOrJql.StartsWith("http")) {
        return $UrlOrJql
    }

    try {
        # Parse the URL
        $uri = [System.Uri]$UrlOrJql

        # Extract query parameters
        $queryParams = [System.Web.HttpUtility]::ParseQueryString($uri.Query)

        # Look for JQL parameter first (common parameter names: jql, jqlQuery)
        $jql = $queryParams["jql"]
        if ([string]::IsNullOrEmpty($jql)) {
            $jql = $queryParams["jqlQuery"]
        }

        # If no JQL found, check for filter parameter
        if ([string]::IsNullOrEmpty($jql)) {
            $filterId = $queryParams["filter"]
            if (-not [string]::IsNullOrEmpty($filterId)) {
                Write-Host "Found filter ID: $filterId. Retrieving JQL from filter..." -ForegroundColor Cyan
                $filterJql = Get-JqlFromFilter -FilterId $filterId
                if ($null -ne $filterJql) {
                    return $filterJql
                }
            }
        }

        if ([string]::IsNullOrEmpty($jql)) {
            Write-Host "Warning: No JQL or filter parameter found in URL. Supported parameters: jql, jqlQuery, filter" -ForegroundColor Yellow
            Write-Host "URL: $UrlOrJql" -ForegroundColor Yellow
            return $null
        }

        # URL decode the JQL
        $decodedJql = [System.Web.HttpUtility]::UrlDecode($jql)
        Write-Host "Extracted JQL from URL: $decodedJql" -ForegroundColor Green
        return $decodedJql
    }
    catch {
        Write-Host "Error parsing URL: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "URL: $UrlOrJql" -ForegroundColor Red
        return $null
    }
}

# Function to get JQL from filter ID
function Get-JqlFromFilter {
    param (
        [string]$FilterId
    )

    try {
        $filterUrl = "$JiraUrl/rest/api/2/filter/$FilterId"
        $filter = Invoke-RestMethod -Uri $filterUrl -Headers $headers -Method GET

        Write-Host "Retrieved filter: $($filter.name)" -ForegroundColor Green
        Write-Host "Filter JQL: $($filter.jql)" -ForegroundColor Green

        return $filter.jql
    }
    catch {
        Write-Host "Error retrieving filter $FilterId`: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Make sure the filter exists and you have permission to view it." -ForegroundColor Yellow
        return $null
    }
}
# Test authentication first
function Test-JiraAuth {
    try {
        $testUrl = "$JiraUrl/rest/api/2/myself"
        $response = Invoke-RestMethod -Uri $testUrl -Headers $headers -Method GET
        Write-Host "Authentication successful. Connected as: $($response.displayName)" -ForegroundColor Green
        return $true
    }
    catch {
        $errorMessage = $_.Exception.Message
        Write-Host "Authentication failed: $errorMessage" -ForegroundColor Red

        # Check for SSL/TLS errors and provide helpful guidance
        if ($errorMessage -like "*SSL/TLS*" -or $errorMessage -like "*trust relationship*" -or $errorMessage -like "*certificate*") {
            Write-Host "" -ForegroundColor Red
            Write-Host "SSL/TLS Certificate Error Detected!" -ForegroundColor Yellow
            Write-Host "This commonly happens when:" -ForegroundColor Yellow
            Write-Host "- Running as SYSTEM account" -ForegroundColor Yellow
            Write-Host "- Using self-signed certificates" -ForegroundColor Yellow
            Write-Host "- Corporate proxy/firewall issues" -ForegroundColor Yellow
            Write-Host "" -ForegroundColor Yellow
            Write-Host "Solution: Add -BypassSSL parameter to your command:" -ForegroundColor Green
            Write-Host ".\Assign-JiraTickets.ps1 -BypassSSL [other parameters...]" -ForegroundColor Green
            Write-Host ""
        }

        return $false
    }
}

# Function to assign a single ticket
function Assign-SingleTicket {
    param (
        [string]$IssueKey
    )
    
    $assignUrl = "$JiraUrl/rest/api/2/issue/$IssueKey/assignee"
    $body = @{
        name = $AssigneeUsername
    } | ConvertTo-Json

    try {
        $result = Invoke-RestMethod -Uri $assignUrl -Headers $headers -Method PUT -Body $body
        Write-Host "Successfully assigned $IssueKey to $AssigneeUsername" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error assigning $IssueKey`: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to add a label to a ticket
function Add-Label {
    param (
        [string]$IssueKey,
        [string]$LabelToAdd
    )
    
    if ([string]::IsNullOrEmpty($LabelToAdd)) {
        return $true  # Skip if no label provided
    }
    
    $updateUrl = "$JiraUrl/rest/api/2/issue/$IssueKey"
    
    # First get current labels to avoid overwriting them
    try {
        $issue = Invoke-RestMethod -Uri "$updateUrl`?fields=labels" -Headers $headers -Method GET
        $currentLabels = $issue.fields.labels
        
        # Check if label already exists
        if ($currentLabels -contains $LabelToAdd) {
            Write-Host "Label '$LabelToAdd' already exists on $IssueKey" -ForegroundColor Yellow
            return $true
        }
        
        # Add the new label to existing ones
        $updatedLabels = $currentLabels + $LabelToAdd
        
        # Update the issue with the new labels
        $body = @{
            update = @{
                labels = @(
                    @{
                        add = $LabelToAdd
                    }
                )
            }
        } | ConvertTo-Json -Depth 5
        
        $result = Invoke-RestMethod -Uri $updateUrl -Headers $headers -Method PUT -Body $body
        Write-Host "Successfully added label '$LabelToAdd' to $IssueKey" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error adding label to $IssueKey`: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to get issues from JQL query or JIRA URL
function Get-IssuesFromJql {
    param (
        [string]$JqlQueryOrUrl,
        [int]$MaxResults = 100
    )

    # Parse JQL from URL or use as-is if it's already JQL
    $actualJql = Get-JqlFromUrl -UrlOrJql $JqlQueryOrUrl
    if ($null -eq $actualJql) {
        Write-Host "Error: Could not extract JQL from the provided input: $JqlQueryOrUrl" -ForegroundColor Red
        return @()
    }

    $searchUrl = "$JiraUrl/rest/api/2/search"
    $allIssues = @()
    $startAt = 0
    $totalIssues = 0

    do {
        $pageSize = 100  # Jira API max page size
        $searchBody = @{
            jql = $actualJql
            maxResults = $pageSize
            startAt = $startAt
            fields = @("key")
        } | ConvertTo-Json

        try {
            $searchResult = Invoke-RestMethod -Uri $searchUrl -Headers $headers -Method POST -Body $searchBody
            $totalIssues = $searchResult.total
            $allIssues += $searchResult.issues
            $startAt += $searchResult.issues.Count

            # Stop if we've reached the MaxResults limit
            if ($allIssues.Count -ge $MaxResults) {
                $allIssues = $allIssues[0..($MaxResults-1)]
                break
            }
        }
        catch {
            Write-Host "Error searching for issues with JQL '$JqlQuery': $($_.Exception.Message)" -ForegroundColor Red
            return @()
        }
    } while ($startAt -lt $totalIssues -and $allIssues.Count -lt $MaxResults)

    return $allIssues
}

# Function to create an issue link
function Add-IssueLink {
    param (
        [string]$FromIssueKey,
        [string]$ToIssueKey,
        [string]$LinkType = "Relates",
        [string]$Comment,
        [bool]$IsInward = $false
    )

    $linkUrl = "$JiraUrl/rest/api/2/issueLink"

    # Determine inward and outward issues based on direction
    if ($IsInward) {
        $inwardIssue = @{ key = $FromIssueKey }
        $outwardIssue = @{ key = $ToIssueKey }
    } else {
        $inwardIssue = @{ key = $ToIssueKey }
        $outwardIssue = @{ key = $FromIssueKey }
    }

    $body = @{
        type = @{
            name = $LinkType
        }
        inwardIssue = $inwardIssue
        outwardIssue = $outwardIssue
    }

    # Add comment if provided
    if (-not [string]::IsNullOrEmpty($Comment)) {
        $body.comment = @{
            body = $Comment
        }
    }

    $jsonBody = $body | ConvertTo-Json -Depth 5

    try {
        $result = Invoke-RestMethod -Uri $linkUrl -Headers $headers -Method POST -Body $jsonBody
        $direction = if ($IsInward) { "from" } else { "to" }
        Write-Host "Successfully linked $FromIssueKey $direction $ToIssueKey ($LinkType)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error linking $FromIssueKey to $ToIssueKey`: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to get sprint by ID
function Get-SprintById {
    param (
        [int]$SprintId
    )

    if ($SprintId -eq 0) {
        return $null
    }

    try {
        $sprintUrl = "$JiraUrl/rest/agile/1.0/sprint/$SprintId"
        $sprint = Invoke-RestMethod -Uri $sprintUrl -Headers $headers -Method GET
        Write-Host "Found sprint: $($sprint.name) (ID: $($sprint.id), State: $($sprint.state))" -ForegroundColor Green
        return $sprint
    }
    catch {
        Write-Host "Error: Could not find sprint with ID $SprintId`: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Function to add issue to sprint
function Add-IssueToSprint {
    param (
        [string]$IssueKey,
        [object]$Sprint
    )

    if ($null -eq $Sprint) {
        return $false
    }

    $sprintUrl = "$JiraUrl/rest/agile/1.0/sprint/$($Sprint.id)/issue"
    $body = @{
        issues = @($IssueKey)
    } | ConvertTo-Json

    try {
        $result = Invoke-RestMethod -Uri $sprintUrl -Headers $headers -Method POST -Body $body
        Write-Host "Successfully added $IssueKey to sprint '$($Sprint.name)'" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error adding $IssueKey to sprint '$($Sprint.name)': $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to process linking for a single issue
function Add-LinksToIssue {
    param (
        [string]$IssueKey
    )

    if ([string]::IsNullOrEmpty($LinkToJql)) {
        return $true  # Skip if no linking JQL provided
    }

    Write-Host "Finding issues to link to $IssueKey using: $LinkToJql" -ForegroundColor Cyan
    $targetIssues = Get-IssuesFromJql -JqlQueryOrUrl $LinkToJql -MaxResults 50

    if ($targetIssues.Count -eq 0) {
        Write-Host "No issues found to link to $IssueKey" -ForegroundColor Yellow
        return $true
    }

    Write-Host "Found $($targetIssues.Count) issues to link to $IssueKey" -ForegroundColor Cyan
    $successCount = 0

    foreach ($targetIssue in $targetIssues) {
        $targetKey = $targetIssue.key

        # Skip linking to self
        if ($targetKey -eq $IssueKey) {
            Write-Host "Skipping self-link for $IssueKey" -ForegroundColor Yellow
            continue
        }

        $linkSuccess = Add-IssueLink -FromIssueKey $IssueKey -ToIssueKey $targetKey -LinkType $LinkType -Comment $LinkComment -IsInward $LinkAsInward.IsPresent
        if ($linkSuccess) { $successCount++ }

        # Small delay to respect rate limits
        Start-Sleep -Milliseconds 500
    }

    Write-Host "Successfully created $successCount of $($targetIssues.Count) links for $IssueKey" -ForegroundColor Green
    return $successCount -eq $targetIssues.Count
}

# Function to process a single issue (assign, label, link, and sprint)
function Process-Issue {
    param (
        [string]$IssueKey,
        [object]$Sprint = $null
    )

    $assignSuccess = $true
    $labelSuccess = $true
    $linkSuccess = $true
    $sprintSuccess = $true

    # Only assign if AssigneeUsername is provided
    if (-not [string]::IsNullOrEmpty($AssigneeUsername)) {
        $assignSuccess = Assign-SingleTicket -IssueKey $IssueKey
    }

    # Add label if specified (independent of assignment)
    if (-not [string]::IsNullOrEmpty($Label)) {
        $labelSuccess = Add-Label -IssueKey $IssueKey -LabelToAdd $Label
    }

    # Create links if specified (independent of assignment)
    if (-not [string]::IsNullOrEmpty($LinkToJql)) {
        $linkSuccess = Add-LinksToIssue -IssueKey $IssueKey
    }

    # Add to sprint if specified (independent of other operations)
    if ($null -ne $Sprint) {
        $sprintSuccess = Add-IssueToSprint -IssueKey $IssueKey -Sprint $Sprint
    }

    return $assignSuccess -and $labelSuccess -and $linkSuccess -and $sprintSuccess
}

# Only proceed if authentication is successful
if (Test-JiraAuth) {
    if ($PSCmdlet.ParameterSetName -eq "SingleTicket") {
        # Look up sprint if specified for single ticket
        $sprint = $null
        if ($SprintId -ne 0) {
            Write-Host "Looking up sprint ID: $SprintId" -ForegroundColor Cyan
            $sprint = Get-SprintById -SprintId $SprintId
            if ($null -eq $sprint) {
                Write-Host "Error: Could not find sprint with ID $SprintId. Aborting operation." -ForegroundColor Red
                return
            }
        }

        # Process a single ticket
        Process-Issue -IssueKey $JiraIssueKey -Sprint $sprint
    }
    else {
        # Parse JQL from URL or use as-is if it's already JQL
        $actualJql = Get-JqlFromUrl -UrlOrJql $JqlQuery
        if ($null -eq $actualJql) {
            Write-Host "Error: Could not extract JQL from the provided input. Please provide either a valid JQL query or a JIRA URL containing a jql parameter." -ForegroundColor Red
            return
        }

        # Search for tickets using JQL and process them
        $searchUrl = "$JiraUrl/rest/api/2/search"
        $allIssues = @()
        $startAt = 0
        $totalIssues = 0

        Write-Host "Starting search with MaxResults set to: $MaxResults" -ForegroundColor Cyan
        Write-Host "Using JQL: $actualJql" -ForegroundColor Cyan

        # Use pagination to get all issues
        do {
            $pageSize = 100  # Jira API max page size
            $searchBody = @{
                jql = $actualJql
                maxResults = $pageSize
                startAt = $startAt
                fields = @("key")
            } | ConvertTo-Json

            try {
                $searchResult = Invoke-RestMethod -Uri $searchUrl -Headers $headers -Method POST -Body $searchBody
                $totalIssues = $searchResult.total
                $allIssues += $searchResult.issues
                $startAt += $searchResult.issues.Count
                
                Write-Host "Retrieved $($searchResult.issues.Count) issues (total found so far: $($allIssues.Count) of $totalIssues)" -ForegroundColor Cyan
                
                # Stop if we've reached the MaxResults limit
                if ($allIssues.Count -ge $MaxResults) {
                    Write-Host "Reached MaxResults limit of $MaxResults" -ForegroundColor Yellow
                    $allIssues = $allIssues[0..($MaxResults-1)]
                    break
                }
            }
            catch {
                Write-Host "Error searching for issues: $($_.Exception.Message)" -ForegroundColor Red
                return
            }
        } while ($startAt -lt $totalIssues -and $allIssues.Count -lt $MaxResults)
        
        $foundIssues = $allIssues.Count
        Write-Host "Found $foundIssues issues (of $totalIssues total matching your query)" -ForegroundColor Cyan
        
        if ($foundIssues -eq 0) {
            Write-Host "No issues to process." -ForegroundColor Yellow
            return
        }
        
        if ($foundIssues -lt $totalIssues) {
            Write-Host "Note: Only processing $foundIssues of $totalIssues total matching issues due to MaxResults limit ($MaxResults)" -ForegroundColor Yellow
        }
        
        # Build confirmation message based on operations to perform
        $operations = @()
        if (-not [string]::IsNullOrEmpty($AssigneeUsername)) {
            $operations += "assign all $foundIssues issues to $AssigneeUsername"
        }
        if (-not [string]::IsNullOrEmpty($Label)) {
            $operations += "add label '$Label'"
        }
        if (-not [string]::IsNullOrEmpty($LinkToJql)) {
            $direction = if ($LinkAsInward.IsPresent) { "inward" } else { "outward" }
            $operations += "create $direction '$LinkType' links using JQL: '$LinkToJql'"
        }
        if ($SprintId -ne 0) {
            $operations += "add to sprint ID $SprintId"
        }

        $operationText = $operations -join " and "

        # Skip confirmation if Force switch is used
        if (-not $Force.IsPresent) {
            $confirmation = Read-Host "Do you want to $operationText? (y/n)"
            if ($confirmation -ne 'y') {
                Write-Host "Operation cancelled by user." -ForegroundColor Yellow
                return
            }
        } else {
            Write-Host "Force mode enabled. Proceeding to $operationText" -ForegroundColor Yellow
        }

        # Look up sprint if specified
        $sprint = $null
        if ($SprintId -ne 0) {
            Write-Host "Looking up sprint ID: $SprintId" -ForegroundColor Cyan
            $sprint = Get-SprintById -SprintId $SprintId
            if ($null -eq $sprint) {
                Write-Host "Error: Could not find sprint with ID $SprintId. Aborting operation." -ForegroundColor Red
                return
            }
        }

        $successCount = 0
        $issueCount = 0
        foreach ($issue in $allIssues) {
            $issueKey = $issue.key
            $issueCount++

            Write-Host "Processing $issueCount of $foundIssues`: $issueKey" -ForegroundColor Cyan
            $success = Process-Issue -IssueKey $issueKey -Sprint $sprint
            if ($success) { $successCount++ }

            # Add a 3-second delay between API calls to respect rate limits
            if ($issueCount -lt $foundIssues) {
                Write-Host "Waiting 3 seconds before processing next ticket (rate limit)..." -ForegroundColor Yellow
                Start-Sleep -Seconds 2
            }
        }
        
        Write-Host "Processing complete. Successfully processed $successCount of $foundIssues issues." -ForegroundColor Green
    }
}
else {
    Write-Host "Aborting operation due to authentication failure." -ForegroundColor Red
}











